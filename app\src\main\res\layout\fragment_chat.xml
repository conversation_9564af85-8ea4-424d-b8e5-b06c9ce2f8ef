<?xml version="1.0" encoding="utf-8"?>
<androidx.drawerlayout.widget.DrawerLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/drawer_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fitsSystemWindows="false"
    tools:openDrawer="start">

    <!-- 主要内容区域 -->
    <LinearLayout
        android:id="@+id/root_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:orientation="vertical"
        android:background="?attr/colorAppBackground"
        android:fitsSystemWindows="false">

        <!-- 头部导航栏 - 现代化设计 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="?attr/colorHeader"
            android:elevation="4dp">

            <LinearLayout
                android:id="@+id/header_layout"
                android:layout_width="match_parent"
                android:layout_height="64dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="20dp">

                <ImageButton
                    android:id="@+id/btn_menu"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_menu"
                    android:contentDescription="菜单"
                    app:tint="@color/white" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="智慧笔记"
                    android:textColor="@color/white"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:gravity="center" />

                <ImageButton
                    android:id="@+id/btn_new_chat"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_add"
                    android:contentDescription="新建会话"
                    app:tint="@color/white" />

            </LinearLayout>

        </LinearLayout>

        <!-- 聊天消息区域 - 优化滤镜效果 -->
        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rv_chat_messages"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:background="?attr/colorAppBackground"
            android:clipToPadding="false"
            android:paddingTop="12dp"
            android:paddingBottom="12dp"
            android:overScrollMode="ifContentScrolls" />

        <!-- 语音录制状态指示器 - 现代化设计 -->
        <LinearLayout
            android:id="@+id/voice_recording_indicator"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginHorizontal="16dp"
            android:layout_marginBottom="8dp"
            android:visibility="gone"
            android:background="@color/voice_recording_background"
            android:elevation="6dp"
            android:orientation="horizontal"
            android:padding="16dp"
            android:gravity="center">

            <TextView
                android:id="@+id/tv_voice_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="按住说话"
                android:textColor="@color/white"
                android:textSize="16sp"
                android:layout_marginEnd="8dp" />

            <ProgressBar
                android:id="@+id/pb_voice_processing"
                style="?android:attr/progressBarStyleSmall"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="gone" />

        </LinearLayout>

        <!-- 输入区域 - 现代化设计 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:elevation="8dp"
            android:background="?attr/colorCardBackground"
            android:orientation="vertical">

            <LinearLayout
                android:id="@+id/input_layout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:paddingHorizontal="20dp"
                android:paddingVertical="16dp"
                android:gravity="center_vertical">

                <ImageButton
                    android:id="@+id/btn_voice"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/bg_voice_button"
                    android:src="@drawable/ic_mic"
                    android:contentDescription="语音输入"
                    app:tint="?attr/colorTextSecondary"
                    android:layout_marginEnd="12dp"
                    android:elevation="2dp" />

                <EditText
                    android:id="@+id/et_message_input"
                    android:layout_width="0dp"
                    android:layout_height="48dp"
                    android:layout_weight="1"
                    android:hint="输入你的问题..."
                    android:background="@drawable/ios_input_background"
                    android:paddingHorizontal="20dp"
                    android:paddingVertical="12dp"
                    android:textSize="16sp"
                    android:textColor="?attr/colorTextPrimary"
                    android:textColorHint="?attr/colorTextSecondary"
                    android:maxLines="3"
                    android:inputType="textMultiLine|textCapSentences"
                    android:layout_marginEnd="12dp" />

                <ImageButton
                    android:id="@+id/btn_send"
                    android:layout_width="48dp"
                    android:layout_height="48dp"
                    android:background="@drawable/bg_send_button"
                    android:src="@drawable/ic_send"
                    android:contentDescription="发送"
                    app:tint="@color/white"
                    android:elevation="3dp" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

    <!-- 侧边导航菜单 -->
    <include
        layout="@layout/navigation_drawer"
        android:layout_width="wrap_content"
        android:layout_height="match_parent"
        android:layout_gravity="start" />

</androidx.drawerlayout.widget.DrawerLayout>
