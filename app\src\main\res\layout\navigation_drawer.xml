<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="280dp"
    android:layout_height="match_parent"
    android:background="?attr/colorCardBackground"
    android:orientation="vertical"
    android:fitsSystemWindows="true">

    <!-- 顶部间距区域 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="60dp" />

    <!-- 分割线 -->
    <View
        android:layout_width="match_parent"
        android:layout_height="1dp"
        android:background="@color/divider_color"
        android:layout_marginHorizontal="16dp"
        android:layout_marginBottom="8dp" />

    <!-- 菜单项 -->
    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:orientation="vertical"
        android:paddingTop="8dp">

        <!-- 对话 -->
        <LinearLayout
            android:id="@+id/nav_chat"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_chat"
                android:tint="@color/header_blue"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="对话"
                android:textSize="16sp"
                android:textColor="@color/text_dark" />

        </LinearLayout>

        <!-- 知识库 -->
        <LinearLayout
            android:id="@+id/nav_knowledge"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_knowledge"
                android:tint="@color/header_blue"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="知识库"
                android:textSize="16sp"
                android:textColor="@color/text_dark" />

        </LinearLayout>

        <!-- 会话列表 -->
        <LinearLayout
            android:id="@+id/nav_conversations"
            android:layout_width="match_parent"
            android:layout_height="56dp"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:paddingHorizontal="16dp"
            android:clickable="true"
            android:focusable="true"
            android:background="?attr/selectableItemBackground">

            <ImageView
                android:layout_width="24dp"
                android:layout_height="24dp"
                android:src="@drawable/ic_chat"
                android:tint="@color/header_blue"
                android:layout_marginEnd="16dp" />

            <TextView
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="会话列表"
                android:textSize="16sp"
                android:textColor="@color/text_dark" />

        </LinearLayout>

        <!-- 分组区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="8dp">

            <!-- 分组标题栏 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="分组"
                    android:textSize="14sp"
                    android:textColor="?attr/colorTextSecondary"
                    android:textStyle="bold" />

                <ImageButton
                    android:id="@+id/btn_add_group"
                    android:layout_width="32dp"
                    android:layout_height="32dp"
                    android:background="?attr/selectableItemBackgroundBorderless"
                    android:src="@drawable/ic_add"
                    android:tint="@color/header_blue"
                    android:contentDescription="新建分组"
                    android:padding="6dp" />

            </LinearLayout>

            <!-- 默认分组 -->
            <LinearLayout
                android:id="@+id/nav_default_group"
                android:layout_width="match_parent"
                android:layout_height="48dp"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:paddingHorizontal="16dp"
                android:clickable="true"
                android:focusable="true"
                android:background="?attr/selectableItemBackground">

                <ImageView
                    android:layout_width="20dp"
                    android:layout_height="20dp"
                    android:src="@drawable/ic_folder"
                    android:tint="@color/ios_text_secondary"
                    android:layout_marginEnd="12dp" />

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="默认分组"
                    android:textSize="15sp"
                    android:textColor="@color/text_dark" />

                <TextView
                    android:id="@+id/tv_default_group_count"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="0"
                    android:textSize="12sp"
                    android:textColor="@color/ios_text_secondary"
                    android:background="@drawable/ios_badge_background"
                    android:paddingHorizontal="6dp"
                    android:paddingVertical="2dp"
                    android:minWidth="20dp"
                    android:gravity="center" />

            </LinearLayout>

            <!-- 分组列表 -->
            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_nav_groups"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="200dp"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

        <!-- 分割线 -->
        <View
            android:layout_width="match_parent"
            android:layout_height="1dp"
            android:background="@color/divider_color"
            android:layout_marginVertical="8dp"
            android:layout_marginHorizontal="16dp" />

        <!-- 默认分组会话列表 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 今天 -->
            <TextView
                android:id="@+id/tv_today_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="今天"
                android:textSize="13sp"
                android:textColor="?attr/colorTextSecondary"
                android:paddingHorizontal="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="4dp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_today_conversations"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="120dp"
                android:nestedScrollingEnabled="false" />

            <!-- 本周 -->
            <TextView
                android:id="@+id/tv_week_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="本周"
                android:textSize="13sp"
                android:textColor="?attr/colorTextSecondary"
                android:paddingHorizontal="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="4dp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_week_conversations"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="120dp"
                android:nestedScrollingEnabled="false" />

            <!-- 本月 -->
            <TextView
                android:id="@+id/tv_older_header"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="本月"
                android:textSize="13sp"
                android:textColor="?attr/colorTextSecondary"
                android:paddingHorizontal="16dp"
                android:paddingTop="12dp"
                android:paddingBottom="4dp"
                android:visibility="gone" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv_older_conversations"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:maxHeight="120dp"
                android:nestedScrollingEnabled="false" />

        </LinearLayout>

    </LinearLayout>

    <!-- 底部设置按钮区域 -->
    <LinearLayout
        android:id="@+id/nav_settings"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:gravity="start|center_vertical"
        android:paddingHorizontal="16dp"
        android:paddingVertical="20dp"
        android:clickable="true"
        android:focusable="true"
        android:background="?attr/selectableItemBackground">

        <!-- 设置按钮 -->
        <ImageView
            android:layout_width="32dp"
            android:layout_height="32dp"
            android:src="@drawable/ic_settings"
            android:tint="@color/header_blue"
            android:background="@drawable/circle_background"
            android:padding="6dp" />

        <!-- 设置文字 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="设置"
            android:textSize="16sp"
            android:textColor="@color/text_dark"
            android:layout_marginStart="12dp" />

    </LinearLayout>

</LinearLayout>
