package com.bei.rag.fragment

import android.Manifest
import android.app.AlertDialog
import android.content.Intent
import android.content.pm.PackageManager
import android.net.Uri
import android.os.Bundle
import android.text.Editable
import android.text.TextWatcher
import android.util.Log
import android.view.LayoutInflater
import android.view.MotionEvent
import android.view.View
import android.view.ViewGroup
import android.widget.Button
import android.widget.EditText
import android.widget.ImageButton
import android.widget.LinearLayout
import android.widget.PopupMenu
import android.widget.ProgressBar
import android.widget.TextView
import android.widget.Toast
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.core.view.GravityCompat
import androidx.drawerlayout.widget.DrawerLayout
import androidx.fragment.app.Fragment
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView
import com.bei.rag.R
import com.bei.rag.adapter.ChatAdapter
import com.bei.rag.adapter.ConversationItem
import com.bei.rag.adapter.NavConversationAdapter
import com.bei.rag.adapter.NavGroupAdapter
import com.bei.rag.database.AppDatabase
import com.bei.rag.model.ChatMessage
import com.bei.rag.model.ConversationGroup
import com.bei.rag.repository.ChatRepository
import com.bei.rag.repository.ConversationGroupRepository
import com.bei.rag.repository.UserRepository
import com.bei.rag.service.*
import com.bei.rag.utils.DataManager
import com.bei.rag.utils.StatusBarHelper
import com.bei.rag.utils.TtsConfig
import com.bei.rag.model.*
import io.noties.markwon.Markwon
import kotlinx.coroutines.Job
import kotlinx.coroutines.launch

class ChatFragment : Fragment() {

    companion object {
        private const val TAG = "ChatFragment"
        private const val ARG_CONVERSATION_ID = "conversation_id"
        private const val ARG_GROUP_ID = "group_id"

        fun newInstance() = ChatFragment()

        fun newInstance(conversationId: Long, groupId: Long = 0): ChatFragment {
            val fragment = ChatFragment()
            val args = Bundle()
            args.putLong(ARG_CONVERSATION_ID, conversationId)
            args.putLong(ARG_GROUP_ID, groupId)
            fragment.arguments = args
            return fragment
        }
    }

    private lateinit var drawerLayout: DrawerLayout
    private lateinit var recyclerView: RecyclerView
    private lateinit var messageInput: EditText
    private lateinit var sendButton: ImageButton
    private lateinit var voiceButton: ImageButton
    private lateinit var newChatButton: ImageButton
    private lateinit var menuButton: ImageButton
    private lateinit var chatAdapter: ChatAdapter
    private lateinit var markwon: Markwon
    private lateinit var apiService: OpenRouterApiService

    // 语音功能相关
    private lateinit var voiceToTextService: VoiceToTextService
    private lateinit var voiceRecordingIndicator: LinearLayout
    private lateinit var voiceStatusText: TextView
    private lateinit var voiceProcessingProgress: ProgressBar
    private var isRecording = false

    // TTS功能相关
    private lateinit var textToSpeechService: TextToSpeechService
    private var currentPlayingMessageId: String? = null
    
    // 导航菜单相关
    private lateinit var navChat: LinearLayout
    private lateinit var navKnowledge: LinearLayout
    private lateinit var navConversations: LinearLayout
    private lateinit var navDefaultGroup: LinearLayout
    private lateinit var navSettings: LinearLayout
    private lateinit var addGroupButton: ImageButton
    private lateinit var defaultGroupCountText: TextView
    private lateinit var navGroupsRecyclerView: RecyclerView
    
    // 会话列表相关
    private lateinit var todayHeader: TextView
    private lateinit var weekHeader: TextView
    private lateinit var olderHeader: TextView
    private lateinit var todayConversationsRv: RecyclerView
    private lateinit var weekConversationsRv: RecyclerView
    private lateinit var olderConversationsRv: RecyclerView
    private lateinit var todayAdapter: NavConversationAdapter
    private lateinit var weekAdapter: NavConversationAdapter
    private lateinit var olderAdapter: NavConversationAdapter
    
    private lateinit var chatRepository: ChatRepository
    private lateinit var userRepository: UserRepository
    private lateinit var dataManager: DataManager
    private lateinit var groupRepository: ConversationGroupRepository
    private lateinit var navGroupAdapter: NavGroupAdapter
    private lateinit var ttsConfig: TtsConfig
    
    // RAG相关服务
    private var ragQueryEngine: RagQueryEngine? = null
    
    private var currentConversationId: Long = 0
    private var currentGroupId: Long = 0
    private var isWaitingForResponse = false
    private var chatHistoryJob: Job? = null

    private val filePickerLauncher = registerForActivityResult(
        ActivityResultContracts.StartActivityForResult()
    ) { result ->
        if (result.resultCode == android.app.Activity.RESULT_OK) {
            result.data?.data?.let { uri ->
                handleFileSelection(uri)
            }
        }
    }

    // 录音权限请求
    private val recordAudioPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted ->
        if (isGranted) {
            startVoiceRecording()
        } else {
            Toast.makeText(requireContext(), "需要录音权限才能使用语音功能", Toast.LENGTH_SHORT).show()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View? {
        return inflater.inflate(R.layout.fragment_chat, container, false)
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        // 获取传入的参数
        arguments?.let { args ->
            currentConversationId = args.getLong(ARG_CONVERSATION_ID, 0)
            currentGroupId = args.getLong(ARG_GROUP_ID, 0)
        }

        // 如果没有传入会话ID，生成新的
        if (currentConversationId == 0L) {
            currentConversationId = System.currentTimeMillis()
        }

        initDatabase()
        setupUI(view)
        setupNavigationMenu()
        setupKeyboardHandling()
        setupVoiceFeature()
        initializeConversation()
        loadNavigationGroups()
        loadNavigationConversations()

        // 延迟设置状态栏适配，确保UI完全初始化
        view.post {
            setupStatusBarForChat(view)
        }
    }

    /**
     * 设置聊天页面的状态栏适配
     * 修复：统一状态栏设置，避免重复调用
     */
    private fun setupStatusBarForChat(view: View) {
        // 设置聊天界面状态栏适配
        StatusBarHelper.setupChatStatusBar(this, view)
    }

    override fun onResume() {
        super.onResume()

        // 在Fragment恢复时重新应用状态栏设置
        // 这可以修复从设置页面返回时的状态栏问题
        view?.let { rootView ->
            // 清理之前的状态栏设置
            StatusBarHelper.cleanupStatusBarOverlay(this, rootView)

            // 重新设置状态栏
            rootView.post {
                setupStatusBarForChat(rootView)
            }
        }
    }

    private fun initDatabase() {
        val database = AppDatabase.getDatabase(requireContext())
        chatRepository = ChatRepository(database.chatMessageDao())
        userRepository = UserRepository(database.userDao())
        dataManager = DataManager(requireContext())
        groupRepository = ConversationGroupRepository(
            database.conversationGroupDao(),
            database.chatMessageDao()
        )
        ttsConfig = TtsConfig(requireContext())
        
        // 初始化RAG相关服务
        initializeRagServices(database)
    }
    
    /**
     * 初始化RAG相关服务
     */
    private fun initializeRagServices(database: AppDatabase) {
        try {
            // 初始化Supabase配置
            try {
                SupabaseConfig.initialize(requireContext())
            } catch (e: Exception) {
                Log.w(TAG, "Supabase初始化失败: ${e.message}")
            }
            
            // 初始化基础服务
            val embeddingService = SiliconFlowEmbeddingService()
            val documentDao = database.documentDao()
            
            // 初始化Supabase相关服务（如果可用）
            val vectorService = if (SupabaseConfig.isInitialized) {
                try {
                    SupabaseVectorService()
                } catch (e: Exception) {
                    Log.w(TAG, "Supabase向量服务初始化失败: ${e.message}")
                    null
                }
            } else null
            
            val vectorSearchService = if (vectorService != null) {
                try {
                    VectorSearchService(
                        context = requireContext(),
                        localDocumentDao = documentDao,
                        vectorService = vectorService,
                        embeddingService = embeddingService
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "向量搜索服务初始化失败: ${e.message}")
                    null
                }
            } else null
            
            // 初始化RAG查询引擎
            ragQueryEngine = if (vectorSearchService != null) {
                try {
                    RagQueryEngine(
                        context = requireContext(),
                        vectorSearchService = vectorSearchService,
                        openRouterApiService = apiService
                    )
                } catch (e: Exception) {
                    Log.w(TAG, "RAG查询引擎初始化失败: ${e.message}")
                    null
                }
            } else null
            
            if (ragQueryEngine != null) {
                Log.d(TAG, "RAG服务初始化成功")
            } else {
                Log.w(TAG, "RAG服务初始化失败，将使用普通聊天模式")
            }
            
        } catch (e: Exception) {
            Log.e(TAG, "RAG服务初始化异常: ${e.message}", e)
        }
    }

    private fun setupUI(view: View) {
        drawerLayout = view.findViewById(R.id.drawer_layout)
        recyclerView = view.findViewById(R.id.rv_chat_messages)
        messageInput = view.findViewById(R.id.et_message_input)
        sendButton = view.findViewById(R.id.btn_send)
        voiceButton = view.findViewById(R.id.btn_voice)
        newChatButton = view.findViewById(R.id.btn_new_chat)
        menuButton = view.findViewById(R.id.btn_menu)

        // 语音功能UI元素
        voiceRecordingIndicator = view.findViewById(R.id.voice_recording_indicator)
        voiceStatusText = view.findViewById(R.id.tv_voice_status)
        voiceProcessingProgress = view.findViewById(R.id.pb_voice_processing)

        // 导航菜单元素
        navChat = view.findViewById(R.id.nav_chat)
        navKnowledge = view.findViewById(R.id.nav_knowledge)
        navConversations = view.findViewById(R.id.nav_conversations)
        navDefaultGroup = view.findViewById(R.id.nav_default_group)
        navSettings = view.findViewById(R.id.nav_settings)
        addGroupButton = view.findViewById(R.id.btn_add_group)
        defaultGroupCountText = view.findViewById(R.id.tv_default_group_count)
        navGroupsRecyclerView = view.findViewById(R.id.rv_nav_groups)
        
        // 会话列表相关
        todayHeader = view.findViewById(R.id.tv_today_header)
        weekHeader = view.findViewById(R.id.tv_week_header)
        olderHeader = view.findViewById(R.id.tv_older_header)
        todayConversationsRv = view.findViewById(R.id.rv_today_conversations)
        weekConversationsRv = view.findViewById(R.id.rv_week_conversations)
        olderConversationsRv = view.findViewById(R.id.rv_older_conversations)

        // 初始化Markwon
        markwon = Markwon.create(requireContext())
        
        // 初始化API服务
        apiService = OpenRouterApiService()

        // 设置RecyclerView
        chatAdapter = ChatAdapter(
            markwon = markwon,
            onRegenerateMessage = { message ->
                regenerateMessage(message)
            },
            onPlayAudio = { message ->
                handleAudioPlayback(message)
            }
        )
        recyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = chatAdapter
        }

        // 设置导航分组RecyclerView
        navGroupAdapter = NavGroupAdapter(
            onGroupClick = { group ->
                openGroupConversations(group)
            },
            onGroupLongClick = { group ->
                showGroupOptionsMenu(group)
            }
        )
        navGroupsRecyclerView.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = navGroupAdapter
        }

        // 设置会话列表RecyclerView
        todayAdapter = NavConversationAdapter { conversation ->
            openConversation(conversation.id)
        }
        weekAdapter = NavConversationAdapter { conversation ->
            openConversation(conversation.id)
        }
        olderAdapter = NavConversationAdapter { conversation ->
            openConversation(conversation.id)
        }

        todayConversationsRv.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = todayAdapter
        }
        weekConversationsRv.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = weekAdapter
        }
        olderConversationsRv.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = olderAdapter
        }

        // 设置按钮监听器
        sendButton.setOnClickListener { sendMessage() }
        newChatButton.setOnClickListener { createNewConversation() }
        menuButton.setOnClickListener { drawerLayout.openDrawer(GravityCompat.START) }

        // 设置输入框监听器
        messageInput.addTextChangedListener(object : TextWatcher {
            override fun beforeTextChanged(s: CharSequence?, start: Int, count: Int, after: Int) {}
            override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            override fun afterTextChanged(s: Editable?) {
                sendButton.isEnabled = s?.isNotBlank() == true && !isWaitingForResponse
            }
        })
    }

    private fun setupNavigationMenu() {
        // 设置DrawerLayout的状态栏适配（延迟执行，避免与主状态栏设置冲突）
        drawerLayout.post {
            StatusBarHelper.setupDrawerStatusBar(this, drawerLayout)
        }

        navChat.setOnClickListener {
            drawerLayout.closeDrawer(GravityCompat.START)
            // 已经在聊天页面，无需操作
        }

        navKnowledge.setOnClickListener {
            showKnowledgeManagementPage()
        }

        navConversations.setOnClickListener {
            showConversationListPage()
        }

        navDefaultGroup.setOnClickListener {
            openDefaultGroupConversations()
        }

        addGroupButton.setOnClickListener {
            showCreateGroupDialog()
        }

        navSettings.setOnClickListener {
            showSettingsPage()
        }
    }

    private fun setupKeyboardHandling() {
        // 键盘处理逻辑
    }

    private fun setupVoiceFeature() {
        // 初始化语音转文字服务
        voiceToTextService = VoiceToTextService(requireContext())

        // 初始化文字转语音服务
        textToSpeechService = TextToSpeechService(requireContext())
        
        // 异步初始化TTS服务
        lifecycleScope.launch {
            textToSpeechService.initialize()
        }

        // 设置语音按钮长按监听
        voiceButton.setOnTouchListener { _, event ->
            when (event.action) {
                MotionEvent.ACTION_DOWN -> {
                    // 检查录音权限
                    if (checkRecordAudioPermission()) {
                        startVoiceRecording()
                    }
                    true
                }
                MotionEvent.ACTION_UP, MotionEvent.ACTION_CANCEL -> {
                    if (isRecording) {
                        stopVoiceRecording()
                    }
                    true
                }
                else -> false
            }
        }
    }

    private fun checkRecordAudioPermission(): Boolean {
        return if (ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.RECORD_AUDIO
            ) == PackageManager.PERMISSION_GRANTED
        ) {
            true
        } else {
            recordAudioPermissionLauncher.launch(Manifest.permission.RECORD_AUDIO)
            false
        }
    }

    private fun startVoiceRecording() {
        if (isRecording) return

        isRecording = true
        showVoiceRecordingUI()

        lifecycleScope.launch {
            try {
                voiceToTextService.startRecording()
            } catch (e: Exception) {
                isRecording = false
                hideVoiceRecordingUI()
                Toast.makeText(requireContext(), "录音启动失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun stopVoiceRecording() {
        if (!isRecording) return

        isRecording = false
        showVoiceProcessingUI()

        lifecycleScope.launch {
            try {
                val result = voiceToTextService.stopRecordingAndTranscribe()
                result.fold(
                    onSuccess = { transcribedText ->
                        hideVoiceRecordingUI()
                        if (transcribedText.isNotBlank()) {
                            messageInput.setText(transcribedText)
                            Toast.makeText(requireContext(), "语音识别成功", Toast.LENGTH_SHORT).show()
                        } else {
                            Toast.makeText(requireContext(), "未识别到语音内容", Toast.LENGTH_SHORT).show()
                        }
                    },
                    onFailure = { exception ->
                        hideVoiceRecordingUI()
                        Toast.makeText(requireContext(), "语音识别失败：${exception.message}", Toast.LENGTH_LONG).show()
                    }
                )
            } catch (e: Exception) {
                hideVoiceRecordingUI()
                Toast.makeText(requireContext(), "处理语音时出错：${e.message}", Toast.LENGTH_LONG).show()
            }
        }
    }

    private fun showVoiceRecordingUI() {
        voiceRecordingIndicator.visibility = View.VISIBLE
        voiceStatusText.text = "松开发送，上滑取消"
        voiceStatusText.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.voice_recording_background))
        voiceProcessingProgress.visibility = View.GONE
    }

    private fun showVoiceProcessingUI() {
        voiceStatusText.text = "正在识别中..."
        voiceStatusText.setBackgroundColor(ContextCompat.getColor(requireContext(), R.color.voice_processing_background))
        voiceProcessingProgress.visibility = View.VISIBLE
    }

    private fun hideVoiceRecordingUI() {
        voiceRecordingIndicator.visibility = View.GONE
        voiceProcessingProgress.visibility = View.GONE
    }

    /**
     * 处理音频播放
     */
    private fun handleAudioPlayback(message: ChatMessage) {
        if (message.isPlaying) {
            // 如果正在播放，则停止播放
            stopAudioPlayback()
        } else {
            // 如果没有播放，则开始播放
            startAudioPlayback(message)
        }
    }

    /**
     * 开始播放音频
     */
    private fun startAudioPlayback(message: ChatMessage) {
        // 停止当前播放的音频
        stopAudioPlayback()

        // 更新UI状态
        currentPlayingMessageId = message.id
        chatAdapter.updateMessagePlayingState(message.id, true)

        lifecycleScope.launch {
            try {
                val result = textToSpeechService.speakText(message.content) {
                    // 播放完成回调
                    stopAudioPlayback()
                }
                result.fold(
                    onSuccess = {
                        // 播放成功，等待播放完成
                        // 播放完成会在回调中处理
                    },
                    onFailure = { exception ->
                        // 播放失败，恢复UI状态
                        stopAudioPlayback()
                        Toast.makeText(requireContext(), "音频播放失败：${exception.message}", Toast.LENGTH_SHORT).show()
                    }
                )
            } catch (e: Exception) {
                stopAudioPlayback()
                Toast.makeText(requireContext(), "音频播放出错：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    /**
     * 停止音频播放
     */
    private fun stopAudioPlayback() {
        textToSpeechService.stopSpeaking()

        // 更新UI状态
        currentPlayingMessageId?.let { messageId ->
            chatAdapter.updateMessagePlayingState(messageId, false)
        }
        currentPlayingMessageId = null
    }

    /**
     * 检查并自动播放AI回复
     */
    private fun checkAndAutoPlayAiReply(aiMessage: ChatMessage) {
        Log.d("ChatFragment", "checkAndAutoPlayAiReply called for message: ${aiMessage.content.take(50)}...")
        
        // 检查是否开启了自动播放设置
        val autoPlayEnabled = ttsConfig.getAutoPlayAiReply()
        Log.d("ChatFragment", "Auto play setting enabled: $autoPlayEnabled")
        
        if (autoPlayEnabled) {
            // 确保当前没有其他音频在播放
            val isCurrentlyPlaying = currentPlayingMessageId != null
            Log.d("ChatFragment", "Currently playing message ID: $currentPlayingMessageId, isPlaying: $isCurrentlyPlaying")
            
            if (!isCurrentlyPlaying) {
                Log.d("ChatFragment", "Starting auto play with delay...")
                // 延迟一段时间再播放，确保UI更新完成和TTS服务准备就绪
                recyclerView.postDelayed({
                    tryAutoPlayWithRetry(aiMessage, 0)
                }, 1000) // 增加延迟到1秒
            } else {
                Log.d("ChatFragment", "Skipping auto play - another message is currently playing")
            }
        } else {
            Log.d("ChatFragment", "Auto play is disabled")
        }
    }

    /**
     * 尝试自动播放，带重试机制
     */
    private fun tryAutoPlayWithRetry(aiMessage: ChatMessage, retryCount: Int) {
        Log.d("ChatFragment", "tryAutoPlayWithRetry attempt: $retryCount")
        
        if (retryCount >= 3) {
            Log.w("ChatFragment", "Auto play failed after 3 attempts")
            Toast.makeText(requireContext(), "自动播放失败，请手动点击播放按钮", Toast.LENGTH_SHORT).show()
            return
        }
        
        // 检查TTS服务是否已初始化
        if (!::textToSpeechService.isInitialized || !textToSpeechService.isInitialized()) {
            Log.w("ChatFragment", "TTS service not ready, retrying in 1 second... (attempt ${retryCount + 1})")
            recyclerView.postDelayed({
                tryAutoPlayWithRetry(aiMessage, retryCount + 1)
            }, 1000)
            return
        }
        
        // 再次检查是否有其他音频在播放
        if (currentPlayingMessageId != null) {
            Log.d("ChatFragment", "Another message started playing, canceling auto play")
            return
        }
        
        Log.d("ChatFragment", "Starting auto playback...")
        startAudioPlayback(aiMessage)
    }

    private fun initializeConversation() {
        loadChatHistory()
    }



    private fun loadChatHistory() {
        // 取消之前的监听
        chatHistoryJob?.cancel()

        chatHistoryJob = lifecycleScope.launch {
            chatRepository.getMessagesByConversation(currentConversationId).collect { messages ->
                chatAdapter.updateMessages(messages)
                if (messages.isNotEmpty()) {
                    scrollToBottom()
                }
            }
        }
    }

    private fun sendMessage() {
        val messageText = messageInput.text.toString().trim()
        if (messageText.isEmpty() || isWaitingForResponse) return

        // 用户发送新消息时立即停止当前播放的语音
        stopAudioPlayback()
        Log.d("ChatFragment", "User sending new message - stopped audio playback")

        // 添加用户消息
        val userMessage = ChatMessage(
            content = messageText,
            isUser = true
        )
        lifecycleScope.launch {
            chatRepository.insertMessage(userMessage, currentConversationId, currentGroupId)
        }

        messageInput.text.clear()
        isWaitingForResponse = true
        sendButton.isEnabled = false

        // 发送API请求（优先使用RAG）
        lifecycleScope.launch {
            try {
                // 尝试使用RAG查询
                val response = if (ragQueryEngine != null) {
                    try {
                        Log.d(TAG, "使用RAG模式处理用户消息")
                        val ragRequest = RagQueryRequest(
                            query = messageText,
                            maxResults = 5,
                            similarityThreshold = 0.3
                        )
                        // 使用完整的RAG流程，包含优化的prompt
                        val (ragResponse, aiResponse) = ragQueryEngine!!.queryWithAiResponse(ragRequest).getOrThrow()
                        
                        // 如果找到了相关文档，在回复中添加来源信息
                        if (ragResponse.results.isNotEmpty()) {
                            val sources = ragResponse.results.mapIndexed { index, result ->
                                "${index + 1}. ${result.documentName}"
                            }.joinToString("\n")
                            
                            "$aiResponse\n\n📚 参考来源：\n$sources"
                        } else {
                            aiResponse
                        }
                    } catch (ragError: Exception) {
                        Log.w(TAG, "RAG查询失败，降级到普通模式: ${ragError.message}")
                        // 降级到普通API调用
                        apiService.sendMessage(messageText).getOrThrow()
                    }
                } else {
                    // 直接使用普通API调用
                    Log.d(TAG, "使用普通模式处理用户消息")
                    apiService.sendMessage(messageText).getOrThrow()
                }
                
                val aiMessage = ChatMessage(
                    content = response,
                    isUser = false
                )
                chatRepository.insertMessage(aiMessage, currentConversationId, currentGroupId)
                
                // 检查是否需要自动播放AI回复
                checkAndAutoPlayAiReply(aiMessage)
                
            } catch (e: Exception) {
                val errorMessage = ChatMessage(
                    content = "抱歉，发生了错误：${e.message}",
                    isUser = false
                )
                chatRepository.insertMessage(errorMessage, currentConversationId, currentGroupId)
                Toast.makeText(requireContext(), "发送失败：${e.message}", Toast.LENGTH_SHORT).show()
            } finally {
                isWaitingForResponse = false
                sendButton.isEnabled = messageInput.text.isNotBlank()
            }
        }
    }

    private fun scrollToBottom() {
        recyclerView.post {
            if (chatAdapter.itemCount > 0) {
                recyclerView.smoothScrollToPosition(chatAdapter.itemCount - 1)
            }
        }
    }

    private fun createNewConversation() {
        // 生成新的会话ID
        currentConversationId = System.currentTimeMillis()

        // 清空当前显示的聊天记录
        chatAdapter.updateMessages(emptyList())

        // 清空输入框
        messageInput.text.clear()

        // 重新加载聊天历史（这会启动新的监听器监听新会话ID）
        loadChatHistory()

        // 显示提示信息
        Toast.makeText(requireContext(), "已创建新会话", Toast.LENGTH_SHORT).show()
    }

    /**
     * 重新生成AI消息
     * 采用方案2：不删除原有回复，在下方新建回复
     * 这样保持对话的完整性，用户可以比较不同的回复
     */
    private fun regenerateMessage(message: ChatMessage) {
        if (isWaitingForResponse) {
            Toast.makeText(requireContext(), "请等待当前回复完成", Toast.LENGTH_SHORT).show()
            return
        }

        // 找到这条AI消息对应的用户消息
        val messageIndex = chatAdapter.getMessageIndex(message)
        if (messageIndex == -1) {
            Toast.makeText(requireContext(), "无法找到对应的消息", Toast.LENGTH_SHORT).show()
            return
        }

        // 找到对应的用户消息（通常是AI消息的前一条）
        val userMessage = chatAdapter.getUserMessageBefore(messageIndex)
        if (userMessage == null) {
            Toast.makeText(requireContext(), "无法找到对应的用户消息", Toast.LENGTH_SHORT).show()
            return
        }

        // 方案2：不删除原有AI消息，直接重新发送用户消息生成新的AI回复
        // 这样用户可以看到完整的对话历史，并比较不同的AI回复
        lifecycleScope.launch {
            try {
                // 重新发送用户消息以获取新的AI回复（会添加到对话末尾）
                sendMessageWithContent(userMessage.content)

                Toast.makeText(requireContext(), "正在重新生成回答...", Toast.LENGTH_SHORT).show()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "重新生成失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun sendMessageWithContent(content: String) {
        if (isWaitingForResponse) return

        // 发送消息时立即停止当前播放的语音
        stopAudioPlayback()
        Log.d("ChatFragment", "Sending message with content - stopped audio playback")

        // 添加用户消息
        val userMessage = ChatMessage(
            content = content,
            isUser = true,
            timestamp = System.currentTimeMillis()
        )

        lifecycleScope.launch {
            try {
                // 保存用户消息
                chatRepository.insertMessage(userMessage, currentConversationId, currentGroupId)

                // 发送到AI并获取回复（优先使用RAG）
                isWaitingForResponse = true

                val aiResponse = if (ragQueryEngine != null) {
                    try {
                        Log.d(TAG, "重新生成时使用RAG模式")
                        val ragRequest = RagQueryRequest(
                            query = content,
                            maxResults = 5,
                            similarityThreshold = 0.3
                        )
                        // 使用完整的RAG流程，包含优化的prompt
                        val (ragResponse, response) = ragQueryEngine!!.queryWithAiResponse(ragRequest).getOrThrow()
                        
                        // 如果找到了相关文档，在回复中添加来源信息
                        if (ragResponse.results.isNotEmpty()) {
                            val sources = ragResponse.results.mapIndexed { index, result ->
                                "${index + 1}. ${result.documentName}"
                            }.joinToString("\n")
                            
                            "$response\n\n📚 参考来源：\n$sources"
                        } else {
                            response
                        }
                    } catch (ragError: Exception) {
                        Log.w(TAG, "RAG重新生成失败，降级到普通模式: ${ragError.message}")
                        apiService.sendMessage(content).getOrThrow()
                    }
                } else {
                    apiService.sendMessage(content).getOrThrow()
                }
                
                val aiMessage = ChatMessage(
                    content = aiResponse,
                    isUser = false,
                    timestamp = System.currentTimeMillis(),
                    sources = emptyList()
                )
                // 保存AI回复
                chatRepository.insertMessage(aiMessage, currentConversationId, currentGroupId)
                
                // 检查是否需要自动播放AI回复
                checkAndAutoPlayAiReply(aiMessage)

            } catch (e: Exception) {
                val errorMessage = ChatMessage(
                    content = "抱歉，发生了错误：${e.message}",
                    isUser = false,
                    timestamp = System.currentTimeMillis()
                )
                chatRepository.insertMessage(errorMessage, currentConversationId, currentGroupId)
            } finally {
                isWaitingForResponse = false
            }
        }
    }

    private fun handleFileSelection(uri: Uri) {
        lifecycleScope.launch {
            try {
                val contentResolver = requireContext().contentResolver
                val cursor = contentResolver.query(uri, null, null, null, null)

                var fileName = "unknown_file"
                var fileSize = 0L

                cursor?.use {
                    if (it.moveToFirst()) {
                        val nameIndex = it.getColumnIndex(android.provider.OpenableColumns.DISPLAY_NAME)
                        val sizeIndex = it.getColumnIndex(android.provider.OpenableColumns.SIZE)

                        if (nameIndex != -1) {
                            fileName = it.getString(nameIndex) ?: "unknown_file"
                        }
                        if (sizeIndex != -1) {
                            fileSize = it.getLong(sizeIndex)
                        }
                    }
                }

                // 复制文件到应用内部存储
                val internalFile = copyFileToInternalStorage(uri, fileName)

                // 保存到数据库
                val database = AppDatabase.getDatabase(requireContext())
                val documentDao = database.documentDao()

                val fileType = getFileType(fileName)
                val document = com.bei.rag.database.entity.DocumentEntity(
                    fileName = fileName,
                    fileType = fileType,
                    filePath = internalFile.absolutePath,
                    fileSize = fileSize,
                    uploadTime = System.currentTimeMillis()
                )

                documentDao.insertDocument(document)
                Toast.makeText(requireContext(), "文档上传成功：$fileName", Toast.LENGTH_SHORT).show()

            } catch (e: Exception) {
                Toast.makeText(requireContext(), "文档上传失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun copyFileToInternalStorage(uri: Uri, fileName: String): java.io.File {
        val contentResolver = requireContext().contentResolver
        val inputStream = contentResolver.openInputStream(uri)

        val documentsDir = java.io.File(requireContext().filesDir, "documents")
        if (!documentsDir.exists()) {
            documentsDir.mkdirs()
        }

        val outputFile = java.io.File(documentsDir, fileName)
        val outputStream = java.io.FileOutputStream(outputFile)

        inputStream?.use { input ->
            outputStream.use { output ->
                input.copyTo(output)
            }
        }

        return outputFile
    }

    private fun getFileType(fileName: String): String {
        return when (fileName.substringAfterLast('.', "").lowercase()) {
            "pdf" -> "pdf"
            "doc", "docx" -> "doc"
            "csv" -> "csv"
            "txt" -> "txt"
            else -> "unknown"
        }
    }

    private fun showKnowledgeManagementPage() {
        // 跳转到知识库页面
        val knowledgeFragment = KnowledgeFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, knowledgeFragment)
            .addToBackStack(null)
            .commit()

        // 关闭导航抽屉
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    private fun showConversationListPage() {
        // 跳转到会话列表页面
        val conversationListFragment = ConversationListFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, conversationListFragment)
            .addToBackStack(null)
            .commit()

        // 关闭导航抽屉
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    private fun loadNavigationGroups() {
        lifecycleScope.launch {
            // 加载默认分组会话数量
            val defaultCount = groupRepository.getDefaultGroupConversationCount()
            defaultGroupCountText.text = defaultCount.toString()
            defaultGroupCountText.visibility = if (defaultCount > 0) View.VISIBLE else View.GONE

            // 加载自定义分组
            groupRepository.getAllGroups().collect { groups ->
                navGroupAdapter.updateGroups(groups)
            }
        }
    }

    private fun showCreateGroupDialog() {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_group, null)

        val nameEditText = dialogView.findViewById<EditText>(R.id.et_group_name)
        val descriptionEditText = dialogView.findViewById<EditText>(R.id.et_group_description)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val createButton = dialogView.findViewById<Button>(R.id.btn_create)

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .create()

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        createButton.setOnClickListener {
            val name = nameEditText.text.toString().trim()
            val description = descriptionEditText.text.toString().trim()

            if (name.isEmpty()) {
                Toast.makeText(requireContext(), "请输入分组名称", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            createGroup(name, description)
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun createGroup(name: String, description: String) {
        lifecycleScope.launch {
            try {
                groupRepository.createGroup(name, description)
                Toast.makeText(requireContext(), "分组创建成功", Toast.LENGTH_SHORT).show()
                loadNavigationGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "创建失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun openDefaultGroupConversations() {
        val fragment = ConversationListFragment.newInstance(0) // 0 表示默认分组
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    private fun openGroupConversations(group: ConversationGroup) {
        val fragment = ConversationListFragment.newInstance(group.id)
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, fragment)
            .addToBackStack(null)
            .commit()
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    private fun showGroupOptionsMenu(group: ConversationGroup) {
        val options = arrayOf("重命名", "删除分组")

        AlertDialog.Builder(requireContext())
            .setTitle(group.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> showRenameGroupDialog(group)
                    1 -> showDeleteGroupDialog(group)
                }
            }
            .show()
    }

    private fun showRenameGroupDialog(group: ConversationGroup) {
        val dialogView = LayoutInflater.from(requireContext())
            .inflate(R.layout.dialog_create_group, null)

        val nameEditText = dialogView.findViewById<EditText>(R.id.et_group_name)
        val descriptionEditText = dialogView.findViewById<EditText>(R.id.et_group_description)
        val cancelButton = dialogView.findViewById<Button>(R.id.btn_cancel)
        val createButton = dialogView.findViewById<Button>(R.id.btn_create)

        // 预填充当前信息
        nameEditText.setText(group.name)
        descriptionEditText.setText(group.description)
        createButton.text = "确认修改"

        val dialog = AlertDialog.Builder(requireContext())
            .setView(dialogView)
            .create()

        cancelButton.setOnClickListener {
            dialog.dismiss()
        }

        createButton.setOnClickListener {
            val newName = nameEditText.text.toString().trim()
            val newDescription = descriptionEditText.text.toString().trim()

            if (newName.isEmpty()) {
                Toast.makeText(requireContext(), "请输入分组名称", Toast.LENGTH_SHORT).show()
                return@setOnClickListener
            }

            renameGroup(group, newName, newDescription)
            dialog.dismiss()
        }

        dialog.show()
    }

    private fun renameGroup(group: ConversationGroup, newName: String, newDescription: String) {
        lifecycleScope.launch {
            try {
                val updatedGroup = group.copy(
                    name = newName,
                    description = newDescription
                )
                groupRepository.updateGroup(updatedGroup)
                Toast.makeText(requireContext(), "分组已重命名", Toast.LENGTH_SHORT).show()
                loadNavigationGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "重命名失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun showDeleteGroupDialog(group: ConversationGroup) {
        AlertDialog.Builder(requireContext())
            .setTitle("删除分组")
            .setMessage("确定要删除分组「${group.name}」吗？\n\n该分组下的所有会话将移动到默认分组。")
            .setPositiveButton("删除") { _, _ ->
                deleteGroup(group)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun deleteGroup(group: ConversationGroup) {
        lifecycleScope.launch {
            try {
                groupRepository.deleteGroup(group.id)
                Toast.makeText(requireContext(), "分组已删除", Toast.LENGTH_SHORT).show()
                loadNavigationGroups()
            } catch (e: Exception) {
                Toast.makeText(requireContext(), "删除失败：${e.message}", Toast.LENGTH_SHORT).show()
            }
        }
    }

    private fun loadNavigationConversations() {
        lifecycleScope.launch {
            try {
                // 获取所有会话ID
                val conversationIds = chatRepository.getAllConversationIds()

                val now = System.currentTimeMillis()
                val oneDayAgo = now - 24 * 60 * 60 * 1000L
                val oneWeekAgo = now - 7 * 24 * 60 * 60 * 1000L
                val oneMonthAgo = now - 30 * 24 * 60 * 60 * 1000L

                // 按时间分组
                val todayConversations = mutableListOf<ConversationItem>()
                val weekConversations = mutableListOf<ConversationItem>()
                val monthConversations = mutableListOf<ConversationItem>()

                conversationIds.forEach { conversationId ->
                    // 获取该会话的第一条用户消息作为标题
                    val messages = chatRepository.getMessagesByConversationSync(conversationId)
                    val firstUserMessage = messages.firstOrNull { it.isUser }
                    val title = firstUserMessage?.content?.take(20) ?: "新会话"

                    val item = ConversationItem(
                        id = conversationId,
                        title = title,
                        timestamp = conversationId // 使用会话ID作为时间戳
                    )

                    when {
                        conversationId >= oneDayAgo -> todayConversations.add(item)
                        conversationId >= oneWeekAgo -> weekConversations.add(item)
                        conversationId >= oneMonthAgo -> monthConversations.add(item)
                    }
                }

                // 更新适配器
                todayAdapter.updateConversations(todayConversations.take(5)) // 最多显示5个
                weekAdapter.updateConversations(weekConversations.take(5))
                olderAdapter.updateConversations(monthConversations.take(5))

                // 控制标题显示
                todayHeader.visibility = if (todayConversations.isNotEmpty()) View.VISIBLE else View.GONE
                todayHeader.text = "今天"

                weekHeader.visibility = if (weekConversations.isNotEmpty()) View.VISIBLE else View.GONE
                weekHeader.text = "本周"

                olderHeader.visibility = if (monthConversations.isNotEmpty()) View.VISIBLE else View.GONE
                olderHeader.text = "本月"

            } catch (e: Exception) {
                // 静默处理错误
            }
        }
    }

    private fun openConversation(conversationId: Long) {
        // 切换到指定会话
        currentConversationId = conversationId
        loadChatHistory()
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    private fun showSettingsPage() {
        val settingsFragment = SettingsFragment.newInstance()
        parentFragmentManager.beginTransaction()
            .replace(R.id.fragment_container, settingsFragment)
            .addToBackStack(null)
            .commit()
        drawerLayout.closeDrawer(GravityCompat.START)
    }

    override fun onDestroyView() {
        super.onDestroyView()
        
        // 清理状态栏遮罩
        view?.let { rootView ->
            StatusBarHelper.cleanupStatusBarOverlay(this, rootView)
        }
        
        // 取消聊天历史监听
        chatHistoryJob?.cancel()

        // 清理语音服务
        if (isRecording) {
            voiceToTextService.cancelRecording()
            isRecording = false
        }

        // 清理TTS服务
        if (::textToSpeechService.isInitialized) {
            textToSpeechService.cleanup()
        }
    }
}
