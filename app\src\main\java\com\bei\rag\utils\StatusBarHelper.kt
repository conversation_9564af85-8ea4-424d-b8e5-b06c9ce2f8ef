package com.bei.rag.utils

import android.graphics.Color
import android.view.View
import android.view.ViewGroup
import android.widget.FrameLayout
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.Fragment
import com.bei.rag.MainActivity
import com.bei.rag.R

/**
 * Fragment状态栏适配辅助类
 * 为Fragment提供状态栏相关的便捷方法
 */
object StatusBarHelper {

    /**
     * 为Fragment设置状态栏适配
     * @param fragment 目标Fragment
     * @param rootView Fragment的根视图
     * @param applyPadding 是否应用顶部内边距
     */
    fun setupStatusBarForFragment(
        fragment: Fragment, 
        rootView: View, 
        applyPadding: Boolean = true
    ) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 应用当前主题的状态栏样式
        statusBarManager.applyThemeBasedStatusBar()
        
        if (applyPadding) {
            // 为根视图添加状态栏高度的顶部内边距
            applyStatusBarPadding(rootView, statusBarManager.getStatusBarHeight())
        }
    }

    /**
     * 为聊天界面设置特殊的状态栏样式
     * 根据聊天背景动态调整，并添加半透明遮罩
     */
    fun setupChatStatusBar(fragment: Fragment, rootView: View) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 获取聊天背景颜色
        val chatBackgroundColor = try {
            rootView.context.getColor(R.attr.colorAppBackground)
        } catch (e: Exception) {
            Color.WHITE // 默认白色
        }
        
        // 根据背景色自动调整状态栏
        statusBarManager.autoAdjustForBackground(chatBackgroundColor)
        
        // 创建半透明遮罩以增强可读性
        if (rootView is ViewGroup) {
            val overlayManager = StatusBarOverlayManager(activity)
            overlayManager.createChatOverlay(rootView, chatBackgroundColor)
        }
        
        // 应用状态栏内边距
        applyStatusBarPadding(rootView, statusBarManager.getStatusBarHeight())
    }

    /**
     * 为设置页面设置透明状态栏
     */
    fun setupTransparentStatusBar(fragment: Fragment, rootView: View) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 设置半透明状态栏
        statusBarManager.setTransparentStatusBar(false)
        
        // 为设置页面添加渐变遮罩
        if (rootView is ViewGroup) {
            val overlayManager = StatusBarOverlayManager(activity)
            val themeColor = try {
                rootView.context.getColor(R.attr.colorThemePrimary)
            } catch (e: Exception) {
                Color.BLUE // 默认蓝色
            }
            overlayManager.createGradientOverlay(rootView, themeColor)
        }
        
        // 应用状态栏内边距
        applyStatusBarPadding(rootView, statusBarManager.getStatusBarHeight())
    }

    /**
     * 为知识库页面设置自定义状态栏
     */
    fun setupKnowledgeStatusBar(fragment: Fragment, rootView: View) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 获取知识库页面的主题色
        val themeColor = try {
            rootView.context.getColor(R.attr.colorThemePrimary)
        } catch (e: Exception) {
            Color.BLUE // 默认蓝色
        }
        
        // 设置自定义状态栏样式
        statusBarManager.setCustomStatusBarForPage(themeColor, false)
        
        // 为知识库页面添加毛玻璃效果遮罩
        if (rootView is ViewGroup) {
            val overlayManager = StatusBarOverlayManager(activity)
            overlayManager.createBlurOverlay(rootView, themeColor)
        }
        
        // 应用状态栏内边距
        applyStatusBarPadding(rootView, statusBarManager.getStatusBarHeight())
    }

    /**
     * 应用状态栏高度的顶部内边距
     * 修复：防止重复应用内边距导致累积问题
     */
    private fun applyStatusBarPadding(view: View, statusBarHeight: Int) {
        // 先清除之前的WindowInsetsListener，防止重复应用
        ViewCompat.setOnApplyWindowInsetsListener(view, null)

        // 重新设置WindowInsetsListener
        ViewCompat.setOnApplyWindowInsetsListener(view) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())

            // 检查是否已经应用过内边距，避免重复应用
            val currentTopPadding = v.paddingTop
            val expectedTopPadding = systemBars.top

            // 只有当前内边距与期望内边距不同时才更新
            if (currentTopPadding != expectedTopPadding) {
                v.setPadding(
                    v.paddingLeft,
                    expectedTopPadding,
                    v.paddingRight,
                    v.paddingBottom
                )
            }
            insets
        }

        // 立即请求应用WindowInsets
        ViewCompat.requestApplyInsets(view)
    }

    /**
     * 创建状态栏占位视图
     * 用于需要手动处理状态栏空间的场景
     */
    fun createStatusBarPlaceholder(fragment: Fragment): View {
        val activity = fragment.activity as? MainActivity ?: return View(fragment.requireContext())
        val statusBarManager = activity.getStatusBarManager()
        
        val placeholder = View(fragment.requireContext())
        val layoutParams = FrameLayout.LayoutParams(
            ViewGroup.LayoutParams.MATCH_PARENT,
            statusBarManager.getStatusBarHeight()
        )
        placeholder.layoutParams = layoutParams
        
        // 设置占位视图的背景色为当前主题的状态栏颜色
        val themeManager = ThemeManager(fragment.requireContext())
        placeholder.setBackgroundColor(themeManager.getCurrentThemeColor())
        
        return placeholder
    }

    /**
     * 为DrawerLayout设置状态栏适配
     * 确保侧滑菜单正确处理状态栏区域，并添加遮罩效果
     */
    fun setupDrawerStatusBar(fragment: Fragment, drawerLayout: androidx.drawerlayout.widget.DrawerLayout) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 设置DrawerLayout的fitsSystemWindows
        drawerLayout.fitsSystemWindows = false // 改为false以便自定义处理
        
        // 创建DrawerLayout专用遮罩
        val overlayManager = StatusBarOverlayManager(activity)
        overlayManager.createDrawerOverlay(drawerLayout)
        
        // 为导航视图设置状态栏内边距
        ViewCompat.setOnApplyWindowInsetsListener(drawerLayout) { v, insets ->
            val systemBars = insets.getInsets(WindowInsetsCompat.Type.systemBars())
            
            // 为导航抽屉设置顶部内边距
            val navigationView = (v as? ViewGroup)?.getChildAt(1) // 通常导航视图是第二个子视图
            navigationView?.setPadding(
                navigationView.paddingLeft,
                systemBars.top,
                navigationView.paddingRight,
                navigationView.paddingBottom
            )
            
            insets
        }
    }

    /**
     * 主题切换时更新Fragment的状态栏
     * @param animated 是否使用动画过渡
     * @param onComplete 动画完成回调
     */
    fun onThemeChanged(fragment: Fragment, rootView: View, animated: Boolean = true, onComplete: (() -> Unit)? = null) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        if (animated) {
            // 使用动画过渡
            statusBarManager.animateModeTransition {
                onComplete?.invoke()
            }
        } else {
            // 直接应用新样式
            statusBarManager.applyThemeBasedStatusBar()
            onComplete?.invoke()
        }
    }

    /**
     * 获取状态栏高度（dp值）
     */
    fun getStatusBarHeightDp(fragment: Fragment): Float {
        val activity = fragment.activity as? MainActivity ?: return 0f
        val statusBarManager = activity.getStatusBarManager()
        val heightPx = statusBarManager.getStatusBarHeight()
        val density = fragment.resources.displayMetrics.density
        return heightPx / density
    }

    /**
     * 检查当前是否为深色模式
     */
    fun isDarkMode(fragment: Fragment): Boolean {
        val nightModeFlags = fragment.resources.configuration.uiMode and 
            android.content.res.Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == android.content.res.Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 为全屏页面设置沉浸式状态栏
     * 适用于图片查看、视频播放等场景
     */
    fun setupImmersiveStatusBar(fragment: Fragment) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        // 设置完全透明的状态栏
        statusBarManager.setTransparentStatusBar(true)
        
        // 根据内容自动调整文字颜色
        val isDark = isDarkMode(fragment)
        statusBarManager.setLightStatusBarContent(!isDark)
    }

    /**
     * 恢复默认状态栏样式
     * 通常在Fragment销毁或切换时调用
     */
    fun restoreDefaultStatusBar(fragment: Fragment) {
        val activity = fragment.activity as? MainActivity ?: return
        val statusBarManager = activity.getStatusBarManager()
        
        statusBarManager.resetToDefault()
    }

    /**
     * 清理Fragment的状态栏遮罩
     * 在Fragment销毁时调用以避免内存泄漏
     * 修复：同时清理WindowInsetsListener
     */
    fun cleanupStatusBarOverlay(fragment: Fragment, rootView: View) {
        val activity = fragment.activity as? MainActivity ?: return

        // 清理WindowInsetsListener，防止内存泄漏和重复应用
        ViewCompat.setOnApplyWindowInsetsListener(rootView, null)

        if (rootView is ViewGroup) {
            val overlayManager = StatusBarOverlayManager(activity)
            overlayManager.cleanup(rootView)
        }
    }

    /**
     * 主题切换时更新所有遮罩
     */
    fun updateOverlaysOnThemeChange(fragment: Fragment, rootView: View) {
        val activity = fragment.activity as? MainActivity ?: return
        
        if (rootView is ViewGroup) {
            val overlayManager = StatusBarOverlayManager(activity)
            overlayManager.onThemeChanged(rootView)
        }
        
        // 同时更新状态栏样式
        onThemeChanged(fragment, rootView)
    }
}